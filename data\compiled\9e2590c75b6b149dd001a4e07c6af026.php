<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo (isset($news['title'])) ? $news['title'] : ""; ?> - <?php echo $site_name ?? ""; ?></title>
    <meta name="keywords" content="<?php if(null !== ($news ?? null) && is_array($news) && array_key_exists('keywords', $news) && !empty($news['keywords'])): ?><?php echo (isset($news['keywords'])) ? $news['keywords'] : ""; ?><?php else: ?><?php echo (isset($news['title'])) ? $news['title'] : ""; ?>,<?php echo $site_name ?? ""; ?>,新闻资讯<?php endif; ?>" />
    <meta name="description" content="<?php if(null !== ($news ?? null) && is_array($news) && array_key_exists('description', $news) && !empty($news['description'])): ?><?php echo (isset($news['description'])) ? $news['description'] : ""; ?><?php else: ?><?php echo (isset($news['title'])) ? $news['title'] : ""; ?> - <?php echo $site_name ?? ""; ?>新闻资讯<?php endif; ?>" />
    <link rel="stylesheet" href="/template/pc/css/common.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/pc/css/news.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
    <style>
        /* 限制文章内容中图片的最大宽度 */
        .news-detail-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 10px auto;
        }
        /* 确保图片容器不会溢出 */
        .news-detail-content {
            overflow: hidden;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
        <!-- 顶部 -->
	<div class="yui-top  yui-1200">
		<div class="yui-top-center">
			<div class="yui-top-left yui-left">
				<a href="https://www.botou.net/">1网站首页</a>
				<a href="#">移动版</a>
				<a href="#">微信公众号</a>
				<a href="#">快速发布</a>
			</div>

			<div class="yui-top-right yui-right yui-text-right">
				<a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">会员中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">我的信息</a></li>
						<li><a href="#">我的收藏</a></li>
						<li><a href="#">账号设置</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">商家中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">商家入驻</a></li>
						<li><a href="#">商家管理</a></li>
						<li><a href="#">营销推广</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">网站导航</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">关于我们</a></li>
						<li><a href="#">联系我们</a></li>
						<li><a href="#">使用帮助</a></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
        <!-- 页面切换导航 -->
        <!-- <div class="page-switch-nav">
            <div class="yui-1200">
                <a href="index.htm" class="active">首页</a>
                <a href="list.htm">列表页</a>
                <a href="view.htm">详情页</a>
            </div>
        </div> -->
	<!-- header-->
	<div class="yui-header yui-1200">

		<div class="yui-t yui-c-box">
			<div class="yui-logo">
				<a href="https://www.botou.net/"><img src="/template/pc/images/logo.png" alt="泊头生活网" srcset=""></a>
			</div>
			<div class="yui-cimg"></div>
			<!--form select -->
			<div class="yui-form">
				<div class="yui-select">
					<!-- <div class="mod_select">
						<div class="select_box">
							<span class="select_txt">信息</span>
							<span class="select-icon"></span>
							<ul class="option">
								<li>信息</li>
								<li>帖子</li>

							</ul>
						</div>
					</div> -->
					<form action="/search.php" method="get" id="header-search-form">

						<input type="hidden" name="show" value="title" />
						<input type="hidden" name="tempid" value="1" />
						<input type="hidden" name="tbname" value="info">
						<input type="text" name="keyword"  class="import" placeholder="请输入关键字" id="header-search-input">
						<input type="submit" class="btn-search" id="header-search-btn" value="搜   索">
					</form>
				</div>
				<div class="yui-select-bottom-text"></div>
			</div>
			<div class="yui-fabu" style="float:right;">
				<button onClick="location.href='/post.php'"><a href="/post.php" target="_blank">免费发布信息</a></button>
			</div>
			<!-- form end -->
		</div>
	</div>
	<div class="yui-clear"></div>
	<div class="yui-nav mt20  yui-1200">
		<ul>
			<li <?php if(!null !== ($current_page ?? null) || $current_page == 'index'): ?>class='nav-cur'<?php endif; ?>><a href="/">首页</a></li>
			<?php 
			// 直接从数据库获取分类数据
			$categories = getCategories();
			
			// 筛选一级分类并排序
			$topCategories = array();
			foreach ($categories as $cat) {
				if ($cat['parent_id'] == 0 && $cat['status'] == 1) {
					$topCategories[] = $cat;
				}
			}
			
			// 按排序值升序排列
			usort($topCategories, function($a, $b) {
				if ($a['sort_order'] == $b['sort_order']) {
					return $a['id'] - $b['id']; // 如果排序值相同，按ID升序
				}
				return $a['sort_order'] - $b['sort_order']; // 按排序值升序
			});
			
			// 输出导航菜单
			foreach ($topCategories as $cat) {
				echo '<li><a href="/'.$cat['pinyin'].'/">'.$cat['name'].'</a></li>';
			}
			 ?>
			<li <?php if(null !== ($current_page ?? null) && $current_page == 'news'): ?>class='nav-cur'<?php endif; ?>><a href="/news.php">新闻中心</a></li>
		</ul>
	</div>

	<script>
	// Header搜索加载状态管理 - 使用多种方式确保兼容性
	(function() {
		function initHeaderSearch() {
			var headerSearchForm = document.getElementById('header-search-form');
			if (headerSearchForm) {
				headerSearchForm.addEventListener('submit', function(e) {
					var input = document.getElementById('header-search-input');
					var keyword = input ? input.value.trim() : '';

					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			}
		}

		function showHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜索中...';
				searchBtn.disabled = true;
				searchBtn.style.backgroundColor = '#6c757d';
				searchBtn.style.cursor = 'not-allowed';

				// 添加调试信息
				console.log('Header搜索加载状态已激活');
			}
		}

		function hideHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜   索';
				searchBtn.disabled = false;
				searchBtn.style.backgroundColor = '#3092d5';
				searchBtn.style.cursor = 'pointer';
			}
		}

		// 多种初始化方式确保兼容性
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', initHeaderSearch);
		} else {
			initHeaderSearch();
		}

		// 如果有jQuery，也用jQuery方式绑定
		if (typeof $ !== 'undefined') {
			$(document).ready(function() {
				$('#header-search-form').on('submit', function(e) {
					var keyword = $('#header-search-input').val().trim();
					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			});
		}

		// 暴露函数到全局作用域，方便调试
		window.showHeaderSearchLoading = showHeaderSearchLoading;
		window.hideHeaderSearchLoading = hideHeaderSearchLoading;
	})();
	</script>

    <div class="yui-clear"></div>
    
    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container">
            <div class="breadcrumb">
                <a href="/">首页</a>
                <span class="separator">></span>
                <a href="/news/">新闻中心</a>
                <?php if(null !== ($parent_cat ?? null) && null !== ($parent_cat ?? null) && !empty($parent_cat)): ?>
                <span class="separator">></span>
                <a href="<?php if($parent_cat['pinyin']): ?>/news/<?php echo (isset($parent_cat['pinyin'])) ? $parent_cat['pinyin'] : ""; ?>/<?php else: ?>/news/?catid=<?php echo (isset($parent_cat['catid'])) ? $parent_cat['catid'] : ""; ?><?php endif; ?>"><?php echo (isset($parent_cat['catname'])) ? $parent_cat['catname'] : ""; ?></a>
                <?php endif; ?>
                <span class="separator">></span>
                <?php if(null !== ($news ?? null) && is_array($news) && array_key_exists('catname', $news) && null !== ($news ?? null) && is_array($news) && array_key_exists('catname', $news) && !empty($news['catname'])): ?>
                <a href="<?php if($all_categories[$news['catid']].pinyin): ?>/news/{$all_categories[$news.catid].pinyin}/<?php else: ?>/news/?catid=<?php echo (isset($news['catid'])) ? $news['catid'] : ""; ?><?php endif; ?>"><?php echo (isset($news['catname'])) ? $news['catname'] : ""; ?></a>
                <span class="separator">></span>
                <?php endif; ?>
                <span class="current">正文</span>
            </div>
        </div>

        <!-- 新闻详情内容区域 - 通栏显示 -->
        <div class="news-detail-container">
            <!-- 新闻详情主体 -->
            <div class="news-detail-main">
                <!-- 新闻标题 -->
                <div class="news-detail-header">
                    <h1 class="news-detail-title"><?php echo (isset($news['title'])) ? $news['title'] : ""; ?></h1>
                    <div class="news-detail-meta">
                        <span><i class="icon-time"></i></span>
                        <?php if($news['author']): ?><span><i class="icon-author"></i>作者: <?php echo (isset($news['author'])) ? $news['author'] : ""; ?></span><?php endif; ?>
                        <?php if($news['catname']): ?><span><i class="icon-cat"></i>栏目: <?php echo (isset($news['catname'])) ? $news['catname'] : ""; ?></span><?php endif; ?>
                        <span><i class="icon-views"></i>浏览: <?php echo (isset($news['click'])) ? $news['click'] : ""; ?></span>
                        <span class="news-share"><i class="icon-share"></i>分享</span>
                    </div>
                </div>

                <!-- 新闻摘要 -->
                <div class="news-detail-summary">
                    <?php if (!empty($news['description'])): ?>
                    <p><?php echo $news['description']; ?></p>
                    <?php endif; ?>
                </div>

                <!-- 新闻正文 -->
                <div class="news-detail-content">
                    <?php echo (isset($news['content'])) ? $news['content'] : ""; ?>
                </div>

                <!-- 分享和点赞 -->
                <div class="news-detail-interaction">
                    <div class="interaction-item like">
                        <i class="icon-like"></i>
                        <span>点赞</span>
                        <em><?php echo (isset($news['click'])) ? $news['click'] : ""; ?></em>
                    </div>
                    <div class="interaction-item collect">
                        <i class="icon-collect"></i>
                        <span>收藏</span>
                        <em><?php echo (isset($news['click'])) ? $news['click'] : ""; ?></em>
                    </div>
                </div>

                <!-- 上一篇下一篇 -->
                <div class="news-detail-nav">
                    <div class="prev-next">
                        <div class="prev">
                            <span>上一篇：</span>
                            <?php if($prev_news): ?>
                            <a href="/news/<?php echo (isset($prev_news['id'])) ? $prev_news['id'] : ""; ?>.html"><?php echo (isset($prev_news['title'])) ? $prev_news['title'] : ""; ?></a>
                            <?php else: ?>
                            <span style="color:#999;">没有了</span>
                            <?php endif; ?>
                        </div>
                        <div class="next">
                            <span>下一篇：</span>
                            <?php if($next_news): ?>
                            <a href="/news/<?php echo (isset($next_news['id'])) ? $next_news['id'] : ""; ?>.html"><?php echo (isset($next_news['title'])) ? $next_news['title'] : ""; ?></a>
                            <?php else: ?>
                            <span style="color:#999;">没有了</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- 相关推荐 -->
                <div class="news-detail-related">
                    <h3 class="related-title"><span>相关推荐</span></h3>
                    <ul class="related-list">
                        <?php if($related_news): ?>
                        <?php if(null !== ($related_news ?? null) && is_array($related_news)): foreach($related_news as $news_item): ?>
                        <li><a href="/news/<?php echo (isset($news_item['id'])) ? $news_item['id'] : ""; ?>.html"><?php echo (isset($news_item['title'])) ? $news_item['title'] : ""; ?></a></li>
                        <?php endforeach; endif; ?>
                        <?php else: ?>
                        <li><span style="color:#999;">暂无相关文章</span></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <div class="yui-footer">
        <div class="yui-1200">
            <div class="footer-content bg-white">
                <p class="footer-nav">
                    <a href="/" title="<?php echo $site_name ?? ""; ?>">网站首页</a>
                    <a href="#" target="_blank">广告服务</a>
                    <a href="#" target="_blank">法律声明</a>
                    <a href="#" target="_blank">网站介绍</a>
                    <a href="#" target="_blank">联系我们</a>
                    <a href="#" target="_blank">招聘信息</a>
                </p>
                <p class="footer-disclaimer">本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
                <p class="footer-copyright">Copyright © 2006-{date('Y')} <a href="/" title="<?php echo $site_name ?? ""; ?>" class="db_link"><?php echo $site_name ?? ""; ?></a> 版权所有</p>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
    <script type="text/javascript" src="/template/pc/js/common.js"></script>
</body>
</html> 