<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1753609306,
  'data' => 
  array (
    'post' => 
    array (
      'id' => 109595,
      'user_id' => 0,
      'category_id' => 47,
      'region_id' => 59,
      'expire_days' => 30,
      'title' => '优质床垫维修65元',
      'is_top_home' => 0,
      'is_top_category' => 0,
      'is_top_subcategory' => 0,
      'top_home_expire' => NULL,
      'top_category_expire' => NULL,
      'top_subcategory_expire' => NULL,
      'view_count' => 2,
      'image_count' => 2,
      'status' => 1,
      'created_at' => 1746493747,
      'updated_at' => 1753607505,
      'expired_at' => 1756199505,
      'is_expired' => 0,
      'category_name' => '司机/物流',
      'category_pinyin' => 'siji',
      'parent_category_name' => '招聘求职',
      'parent_category_pinyin' => 'zhaopin',
      'parent_category_id' => 2,
      'region_name' => '通州区',
      'content' => '可以自取，也可以帮忙送货上门，看情况收取配送费用。

可以自取，也可以帮忙送货上门，看情况收取配送费用。

商品性价比高，使用起来非常方便，有需要的朋友可以联系我。

商品性价比高，使用起来非常方便，有需要的朋友可以联系我。

物品九成新，几乎没有使用痕迹，包装齐全，说明书配件都有。

商品性价比高，使用起来非常方便，有需要的朋友可以联系我。',
      'fields_data' => '',
      'ip' => '**************',
      'password' => 'e10adc3949ba59abbe56e057f20f883e',
      'contact_name' => '郑娜',
      'contact_mobile' => '18749083461',
      'contact_weixin' => 'wx_5fvia4lq5o',
      'contact_address' => '大连市高新区解放路837号',
      'fields' => 
      array (
      ),
      'mobile' => '18749083461',
      'wechat' => 'wx_5fvia4lq5o',
    ),
    'images' => 
    array (
      0 => 
      array (
        'id' => 164370,
        'post_id' => 109595,
        'type' => 'post',
        'file_path' => 'uploads/images/202505/68196133e226a.jpg',
        'thumb_path' => 'uploads/thumbs/202505/68196133e226a.jpg',
        'file_size' => 46,
        'file_type' => 'image/jpeg',
        'sort_order' => 0,
        'created_at' => 1746493747,
      ),
    ),
    'related_posts' => 
    array (
      0 => 
      array (
        'id' => 110989,
        'title' => '正品床垫收购71元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://m.fenlei.com/siji/110989.html',
      ),
      1 => 
      array (
        'id' => 110983,
        'title' => '高档电视置换59元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://m.fenlei.com/siji/110983.html',
      ),
      2 => 
      array (
        'id' => 110967,
        'title' => '二手手机维修99元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://m.fenlei.com/siji/110967.html',
      ),
      3 => 
      array (
        'id' => 110936,
        'title' => '豪华空调配送10元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://m.fenlei.com/siji/110936.html',
      ),
      4 => 
      array (
        'id' => 110928,
        'title' => '全新电脑处理24元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://m.fenlei.com/siji/110928.html',
      ),
      5 => 
      array (
        'id' => 110856,
        'title' => '精品洗衣机配送2元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://m.fenlei.com/siji/110856.html',
      ),
      6 => 
      array (
        'id' => 110838,
        'title' => '实惠沙发转让92元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://m.fenlei.com/siji/110838.html',
      ),
      7 => 
      array (
        'id' => 110823,
        'title' => '二手电视安装62元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://m.fenlei.com/siji/110823.html',
      ),
      8 => 
      array (
        'id' => 110682,
        'title' => '特价桌椅置换46元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://m.fenlei.com/siji/110682.html',
      ),
      9 => 
      array (
        'id' => 110553,
        'title' => '优质车辆出租86元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://m.fenlei.com/siji/110553.html',
      ),
    ),
    'category_info' => 
    array (
      'id' => '47',
      'parent_id' => '2',
      'name' => '司机/物流',
      'icon' => NULL,
      'sort_order' => '4',
      'status' => '1',
      'pinyin' => 'siji',
      'seo_title' => NULL,
      'seo_keywords' => NULL,
      'seo_description' => NULL,
      'template' => NULL,
      'detail_template' => NULL,
      'subcategory_ids' => '',
    ),
    'cached_at' => 1753607506,
  ),
);
