<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1753608335,
  'data' => 
  array (
    'news_list' => 
    array (
      0 => 
      array (
        'id' => '14838',
        'catid' => '8',
        'title' => '测试文章831 - 子栏目',
        'author' => '系统管理员',
        'click' => '627',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126689',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      1 => 
      array (
        'id' => '37483',
        'catid' => '8',
        'title' => '测试文章476 - 子栏目',
        'author' => '系统管理员',
        'click' => '937',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126627',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      2 => 
      array (
        'id' => '35128',
        'catid' => '8',
        'title' => '测试文章121 - 子栏目',
        'author' => '系统管理员',
        'click' => '153',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125949',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      3 => 
      array (
        'id' => '8327',
        'catid' => '8',
        'title' => '测试文章320 - 子栏目',
        'author' => '系统管理员',
        'click' => '362',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125899',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      4 => 
      array (
        'id' => '23382',
        'catid' => '8',
        'title' => '测试文章375 - 子栏目',
        'author' => '系统管理员',
        'click' => '104',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125825',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      5 => 
      array (
        'id' => '28875',
        'catid' => '8',
        'title' => '测试文章868 - 子栏目',
        'author' => '系统管理员',
        'click' => '945',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125803',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      6 => 
      array (
        'id' => '24811',
        'catid' => '8',
        'title' => '测试文章804 - 子栏目',
        'author' => '系统管理员',
        'click' => '638',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125686',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      7 => 
      array (
        'id' => '27460',
        'catid' => '8',
        'title' => '测试文章453 - 子栏目',
        'author' => '系统管理员',
        'click' => '457',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125646',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      8 => 
      array (
        'id' => '26596',
        'catid' => '8',
        'title' => '测试文章589 - 子栏目',
        'author' => '系统管理员',
        'click' => '617',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125379',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      9 => 
      array (
        'id' => '39567',
        'catid' => '8',
        'title' => '测试文章560 - 子栏目',
        'author' => '系统管理员',
        'click' => '207',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125280',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
    ),
    'total_row' => 
    array (
      'total' => '10983',
    ),
  ),
);
