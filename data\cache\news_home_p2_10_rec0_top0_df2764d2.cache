<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1753610494,
  'data' => 
  array (
    'news_list' => 
    array (
      0 => 
      array (
        'id' => '24853',
        'catid' => '7',
        'title' => '测试文章846 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '279',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126406',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      1 => 
      array (
        'id' => '2057',
        'catid' => '4',
        'title' => '测试文章50 - 泊头新闻',
        'author' => '系统管理员',
        'click' => '3',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126368',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻',
      ),
      2 => 
      array (
        'id' => '8815',
        'catid' => '6',
        'title' => '测试文章808 - 新闻资讯',
        'author' => '系统管理员',
        'click' => '710',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126235',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯',
      ),
      3 => 
      array (
        'id' => '33228',
        'catid' => '6',
        'title' => '测试文章221 - 新闻资讯',
        'author' => '系统管理员',
        'click' => '309',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126065',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯',
      ),
      4 => 
      array (
        'id' => '38677',
        'catid' => '4',
        'title' => '测试文章670 - 泊头新闻',
        'author' => '系统管理员',
        'click' => '349',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126035',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻',
      ),
      5 => 
      array (
        'id' => '3298',
        'catid' => '7',
        'title' => '测试文章291 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '492',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125997',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      6 => 
      array (
        'id' => '35128',
        'catid' => '8',
        'title' => '测试文章121 - 子栏目',
        'author' => '系统管理员',
        'click' => '153',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125949',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      7 => 
      array (
        'id' => '8327',
        'catid' => '8',
        'title' => '测试文章320 - 子栏目',
        'author' => '系统管理员',
        'click' => '362',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125899',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      8 => 
      array (
        'id' => '2624',
        'catid' => '6',
        'title' => '测试文章617 - 新闻资讯',
        'author' => '系统管理员',
        'click' => '591',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125831',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯',
      ),
      9 => 
      array (
        'id' => '23382',
        'catid' => '8',
        'title' => '测试文章375 - 子栏目',
        'author' => '系统管理员',
        'click' => '104',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125825',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
    ),
    'total_row' => 
    array (
      'total' => '43882',
    ),
  ),
);
