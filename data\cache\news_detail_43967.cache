<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1753609105,
  'data' => 
  array (
    'id' => 43967,
    'catid' => 7,
    'title' => '测试文章960 - 泊头新闻的子栏目',
    'imgurl' => NULL,
    'author' => '系统管理员',
    'click' => 14,
    'is_recommend' => 1,
    'is_top' => 1,
    'is_show' => 1,
    'addtime' => 1746399341,
    'update_time' => 1754284013,
    'description' => '这是第960篇测试文章的内容。所属栏目：泊头新闻的子栏目生成时间：2025-05-13 18:17:28这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。',
    'keywords' => '',
    'thumb' => '/uploads/images/2025/0804/1754283953145511.jpg',
    'tags' => '',
    'view_count' => 0,
    'catname' => '泊头新闻的子栏目',
    'parentid' => 4,
    'content' => '<p>这是第960篇测试文章的内容。</p><p>所属栏目：泊头新闻的子栏目</p><p><img src="/uploads/images/2025/0804/1754283953995482.jpg" style=""/></p><p><img src="/uploads/images/2025/0804/1754283953145511.jpg" style=""/></p><p><img src="/uploads/images/2025/0804/1754283953361638.jpg" style=""/></p><p><img src="/uploads/images/2025/0804/1754283953470558.jpg" style=""/></p><p><img src="/uploads/images/2025/0804/1754283954308952.jpg" style=""/></p><p><img src="/uploads/images/2025/0804/1754283954937643.jpg" style=""/></p><p>生成时间：2025-05-13 18:17:28</p><p>这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。</p>',
  ),
);
