<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1753608505,
  'data' => 
  array (
    'news_list' => 
    array (
      0 => 
      array (
        'id' => '19650',
        'catid' => '7',
        'title' => '测试文章643 - 泊头新闻的子栏目2222',
        'author' => '系统管理员',
        'click' => '351',
        'is_top' => '1',
        'is_recommend' => '1',
        'addtime' => '1747126777',
        'description' => 'meta公司近日宣布了一项重大人事任命，赵晟佳将担任其全新成立的“超级智能实验室”（MSL）的首席科学家。赵晟佳，这位在AI界迅速崭露头角的年轻科学家，曾是OpenAI的核心研究员，参与了ChatGPT、GPT-4等多个重要AI模型的研发，与OpenAI联合创始人Ilya Sutskever共同被视为o1模型的“奠基者”。meta首席执行官马克·扎克伯格亲自宣布了这一消息，称赞赵晟佳为新实验室的联',
        'thumb' => '/uploads/images/2025/0726/1753510734555963.jpg',
        'catname' => '泊头新闻的子栏目',
      ),
      1 => 
      array (
        'id' => '15512',
        'catid' => '7',
        'title' => '测试文章505 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '148',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126723',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      2 => 
      array (
        'id' => '16647',
        'catid' => '7',
        'title' => '测试文章640 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '473',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126707',
        'description' => '这是第640篇测试文章的内容。所属栏目：泊头新闻的子栏目生成时间：2025-05-13 18:14:59这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      3 => 
      array (
        'id' => '30434',
        'catid' => '7',
        'title' => '测试文章427 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '668',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126651',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      4 => 
      array (
        'id' => '40746',
        'catid' => '7',
        'title' => '测试文章739 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '576',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126596',
        'description' => '这是第739篇测试文章的内容。所属栏目：泊头新闻的子栏目生成时间：2025-05-13 18:17:12这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。222222222',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      5 => 
      array (
        'id' => '24853',
        'catid' => '7',
        'title' => '测试文章846 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '279',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126406',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      6 => 
      array (
        'id' => '3298',
        'catid' => '7',
        'title' => '测试文章291 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '492',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125997',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      7 => 
      array (
        'id' => '33960',
        'catid' => '7',
        'title' => '测试文章953 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '890',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125114',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      8 => 
      array (
        'id' => '11315',
        'catid' => '7',
        'title' => '测试文章308 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '642',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747124853',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      9 => 
      array (
        'id' => '17015',
        'catid' => '7',
        'title' => '测试文章8 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '790',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747124827',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
    ),
    'total_row' => 
    array (
      'total' => '11021',
    ),
  ),
);
