<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{$page_title} - {$site_name}</title>
    <meta name="keywords" content="{$site_name},{$page_title},新闻资讯,本地新闻" />
    <meta name="description" content="{$site_name}{$page_title}，提供最新{$page_title}相关资讯。" />
    <link rel="stylesheet" href="/template/pc/css/common.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/pc/css/news.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
</head>
<body>
    {include file="header.htm"}

    <div class="yui-clear"></div>
    <!-- 面包屑导航 -->
    <div class="yui-1200">
        <div class="breadcrumb-container">
            <div class="breadcrumb">
                <a href="/">首页</a>
                <span class="separator">></span>
                <span>{$page_title}</span>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <div class="news-container">
            <!-- 左侧分类导航 -->
            <div class="news-sidebar">
                <div class="category-nav">
                    <ul class="category-list">
                        <li class="category-item {if $current_page == 'news' && !$catid}active{/if}">
                            <a href="/news/">
                                <span class="icon"></span>
                                <span>热门</span>
                            </a>
                        </li>
                        {if $all_categories}
                        {foreach from=$all_categories item=cat}
                        {if $cat.parentid == 0 && $cat.is_show == 1}
                        <li class="category-item {if $catid == $cat.catid}active{/if}">
                            <a href="{if $cat.pinyin}/news/{$cat.pinyin}/{else}/news/?catid={$cat.catid}{/if}">
                                <span class="icon"></span>
                                <span>{$cat.catname}</span>
                            </a>
                        </li>
                        {/if}
                        {/foreach}
                        {/if}
                    </ul>
                </div>
            </div>

            <!-- 中间新闻列表 -->
            <div class="news-main">


                <!-- 筛选按钮 -->
                <div class="news-filter">
                    <div class="filter-buttons">
                        {if $catid > 0}
                        {if $current_cat.pinyin}
                        <a href="/news/{$current_cat.pinyin}/" class="filter-btn {if $filter_recommend == 0 && $filter_top == 0}active{/if}">全部</a>
                        <a href="/news/{$current_cat.pinyin}/?recommend=1" class="filter-btn {if $filter_recommend == 1}active{/if}">推荐</a>
                        <a href="/news/{$current_cat.pinyin}/?top=1" class="filter-btn {if $filter_top == 1}active{/if}">置顶</a>
                        {else}
                        <a href="/news/?catid={$catid}" class="filter-btn {if $filter_recommend == 0 && $filter_top == 0}active{/if}">全部</a>
                        <a href="/news/?catid={$catid}&recommend=1" class="filter-btn {if $filter_recommend == 1}active{/if}">推荐</a>
                        <a href="/news/?catid={$catid}&top=1" class="filter-btn {if $filter_top == 1}active{/if}">置顶</a>
                        {/if}
                        {else}
                        <a href="/news/" class="filter-btn {if $filter_recommend == 0 && $filter_top == 0}active{/if}">全部</a>
                        <a href="/news/?recommend=1" class="filter-btn {if $filter_recommend == 1}active{/if}">推荐</a>
                        <a href="/news/?top=1" class="filter-btn {if $filter_top == 1}active{/if}">置顶</a>
                        {/if}
                    </div>
                </div>

                <div class="news-list">
                    {if $news_list}
                    {loop $news_list $news}
                    <!-- 统一新闻列表项 -->
                    <div class="news-item {if isset($news.thumb) && $news.thumb}has-thumb{else}no-thumb{/if}">
                        {if isset($news.thumb) && $news.thumb}
                        <!-- 左侧缩略图 -->
                        <div class="news-thumb">
                            <a href="/news/{$news.id}.html">
                                <img src="{$news.thumb}" alt="{$news.title}">
                            </a>
                        </div>
                        {/if}

                        <!-- 新闻内容 -->
                        <div class="news-content">
                            <h3>
                                <a href="/news/{$news.id}.html">{$news.title}</a>
                                {if $news.is_top}<span class="top-tag">置顶</span>{/if}
                                {if $news.is_recommend}<span class="rec-tag">推荐</span>{/if}
                            </h3>
                            <div class="meta">
                                <span>作者：{if $news.author}{$news.author}{else}admin{/if}</span>
                                <span>浏览：{$news.click} 人浏览</span>
                                <span><?php echo date('Y-m-d', $news['addtime']); ?></span>
                            </div>
                            <div class="desc">
                                <?php
                                if (!empty($news['description'])) {
                                    echo cut_str($news['description'], 150);
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    {/loop}
                    {else}
                    <div class="empty-list" style="padding: 50px 0; text-align: center; color: #999;">
                        <p>暂无相关新闻</p>
                    </div>
                    {/if}
                </div>

                <!-- 分页 -->
                {if $pagebar}
                {$pagebar}
                {/if}
            </div>


        </div>
    </div>
    
    {include file="footer.htm"}

    <script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
    <script type="text/javascript" src="/template/pc/js/common.js"></script>

    <script>
    // 设置当前菜单项激活状态
    $(document).ready(function() {
        var currentUrl = window.location.href;
        var currentPath = window.location.pathname + window.location.search;

        $('.category-item a').each(function() {
            var linkUrl = $(this).attr('href');
            var linkPath = linkUrl.replace(window.location.origin, '');

            if (currentPath === linkPath ||
                (currentPath.indexOf('catid=') !== -1 && linkUrl.indexOf(currentPath.split('catid=')[1]) !== -1)) {
                $(this).parent().addClass('active');
            }
        });

        // 如果没有匹配的，默认激活第一个（热门）
        if (!$('.category-item.active').length) {
            $('.category-item').first().addClass('active');
        }
    });
    </script>
</body>
</html> 