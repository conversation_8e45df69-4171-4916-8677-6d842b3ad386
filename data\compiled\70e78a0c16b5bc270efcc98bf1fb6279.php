<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo $page_title ?? ""; ?> - <?php echo $site_name ?? ""; ?></title>
    <meta name="keywords" content="<?php echo $site_name ?? ""; ?>,<?php echo $page_title ?? ""; ?>,新闻资讯,本地新闻" />
    <meta name="description" content="<?php echo $site_name ?? ""; ?><?php echo $page_title ?? ""; ?>，提供最新<?php echo $page_title ?? ""; ?>相关资讯。" />
    <link rel="stylesheet" href="/template/pc/css/common.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/pc/css/news.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
</head>
<body>
        <!-- 顶部 -->
	<div class="yui-top  yui-1200">
		<div class="yui-top-center">
			<div class="yui-top-left yui-left">
				<a href="https://www.botou.net/">1网站首页</a>
				<a href="#">移动版</a>
				<a href="#">微信公众号</a>
				<a href="#">快速发布</a>
			</div>

			<div class="yui-top-right yui-right yui-text-right">
				<a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">会员中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">我的信息</a></li>
						<li><a href="#">我的收藏</a></li>
						<li><a href="#">账号设置</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">商家中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">商家入驻</a></li>
						<li><a href="#">商家管理</a></li>
						<li><a href="#">营销推广</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">网站导航</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">关于我们</a></li>
						<li><a href="#">联系我们</a></li>
						<li><a href="#">使用帮助</a></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
        <!-- 页面切换导航 -->
        <!-- <div class="page-switch-nav">
            <div class="yui-1200">
                <a href="index.htm" class="active">首页</a>
                <a href="list.htm">列表页</a>
                <a href="view.htm">详情页</a>
            </div>
        </div> -->
	<!-- header-->
	<div class="yui-header yui-1200">

		<div class="yui-t yui-c-box">
			<div class="yui-logo">
				<a href="https://www.botou.net/"><img src="/template/pc/images/logo.png" alt="泊头生活网" srcset=""></a>
			</div>
			<div class="yui-cimg"></div>
			<!--form select -->
			<div class="yui-form">
				<div class="yui-select">
					<!-- <div class="mod_select">
						<div class="select_box">
							<span class="select_txt">信息</span>
							<span class="select-icon"></span>
							<ul class="option">
								<li>信息</li>
								<li>帖子</li>

							</ul>
						</div>
					</div> -->
					<form action="/search.php" method="get" id="header-search-form">

						<input type="hidden" name="show" value="title" />
						<input type="hidden" name="tempid" value="1" />
						<input type="hidden" name="tbname" value="info">
						<input type="text" name="keyword"  class="import" placeholder="请输入关键字" id="header-search-input">
						<input type="submit" class="btn-search" id="header-search-btn" value="搜   索">
					</form>
				</div>
				<div class="yui-select-bottom-text"></div>
			</div>
			<div class="yui-fabu" style="float:right;">
				<button onClick="location.href='/post.php'"><a href="/post.php" target="_blank">免费发布信息</a></button>
			</div>
			<!-- form end -->
		</div>
	</div>
	<div class="yui-clear"></div>
	<div class="yui-nav mt20  yui-1200">
		<ul>
			<li <?php if(!null !== ($current_page ?? null) || $current_page == 'index'): ?>class='nav-cur'<?php endif; ?>><a href="/">首页</a></li>
			<?php 
			// 直接从数据库获取分类数据
			$categories = getCategories();
			
			// 筛选一级分类并排序
			$topCategories = array();
			foreach ($categories as $cat) {
				if ($cat['parent_id'] == 0 && $cat['status'] == 1) {
					$topCategories[] = $cat;
				}
			}
			
			// 按排序值升序排列
			usort($topCategories, function($a, $b) {
				if ($a['sort_order'] == $b['sort_order']) {
					return $a['id'] - $b['id']; // 如果排序值相同，按ID升序
				}
				return $a['sort_order'] - $b['sort_order']; // 按排序值升序
			});
			
			// 输出导航菜单
			foreach ($topCategories as $cat) {
				echo '<li><a href="/'.$cat['pinyin'].'/">'.$cat['name'].'</a></li>';
			}
			 ?>
			<li <?php if(null !== ($current_page ?? null) && $current_page == 'news'): ?>class='nav-cur'<?php endif; ?>><a href="/news.php">新闻中心</a></li>
		</ul>
	</div>

	<script>
	// Header搜索加载状态管理 - 使用多种方式确保兼容性
	(function() {
		function initHeaderSearch() {
			var headerSearchForm = document.getElementById('header-search-form');
			if (headerSearchForm) {
				headerSearchForm.addEventListener('submit', function(e) {
					var input = document.getElementById('header-search-input');
					var keyword = input ? input.value.trim() : '';

					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			}
		}

		function showHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜索中...';
				searchBtn.disabled = true;
				searchBtn.style.backgroundColor = '#6c757d';
				searchBtn.style.cursor = 'not-allowed';

				// 添加调试信息
				console.log('Header搜索加载状态已激活');
			}
		}

		function hideHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜   索';
				searchBtn.disabled = false;
				searchBtn.style.backgroundColor = '#3092d5';
				searchBtn.style.cursor = 'pointer';
			}
		}

		// 多种初始化方式确保兼容性
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', initHeaderSearch);
		} else {
			initHeaderSearch();
		}

		// 如果有jQuery，也用jQuery方式绑定
		if (typeof $ !== 'undefined') {
			$(document).ready(function() {
				$('#header-search-form').on('submit', function(e) {
					var keyword = $('#header-search-input').val().trim();
					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			});
		}

		// 暴露函数到全局作用域，方便调试
		window.showHeaderSearchLoading = showHeaderSearchLoading;
		window.hideHeaderSearchLoading = hideHeaderSearchLoading;
	})();
	</script>


    <div class="yui-clear"></div>
    <!-- 面包屑导航 -->
    <div class="yui-1200">
        <div class="breadcrumb-container">
            <div class="breadcrumb">
                <a href="/">首页</a>
                <span class="separator">></span>
                <a href="/news/">新闻中心</a>
                <?php if($catid > 0): ?>
                    {assign var="current_cat" value=$all_categories[$catid]}
                    <?php if(null !== ($current_cat ?? null) && is_array($current_cat) && array_key_exists('parentid', $current_cat) && $current_cat['parentid'] > 0): ?>
                        {assign var="parent_cat" value=$all_categories[$current_cat.parentid]}
                        <span class="separator">></span>
                        <a href="<?php if($parent_cat['pinyin']): ?>/news/<?php echo (isset($parent_cat['pinyin'])) ? $parent_cat['pinyin'] : ""; ?>/<?php else: ?>/news/?catid=<?php echo (isset($parent_cat['catid'])) ? $parent_cat['catid'] : ""; ?><?php endif; ?>"><?php echo (isset($parent_cat['catname'])) ? $parent_cat['catname'] : ""; ?></a>
                    <?php endif; ?>
                    <span class="separator">></span>
                    <span class="current"><?php echo (isset($current_cat['catname'])) ? $current_cat['catname'] : ""; ?></span>
                <?php else: ?>
                    <span class="separator">></span>
                    <span class="current">热门新闻</span>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <div class="news-container">
            <!-- 左侧分类导航 -->
            <div class="news-sidebar">
                <div class="category-nav">
                    <ul class="category-list">
                        <li class="category-item <?php if($current_page == 'news' && !$catid): ?>active<?php endif; ?>">
                            <a href="/news/">
                                <span class="icon"></span>
                                <span>热门</span>
                            </a>
                        </li>
                        <?php if($all_categories): ?>
                        <?php if(null !== ($all_categories ?? null) && is_array($all_categories)): foreach($all_categories as $cat): ?>
                        <?php if($cat['parentid'] == 0 && $cat['is_show'] == 1): ?>
                        <li class="category-item <?php if($catid == $cat['catid']): ?>active<?php endif; ?>">
                            <a href="<?php if($cat['pinyin']): ?>/news/<?php echo (isset($cat['pinyin'])) ? $cat['pinyin'] : ""; ?>/<?php else: ?>/news/?catid=<?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?><?php endif; ?>">
                                <span class="icon"></span>
                                <span><?php echo (isset($cat['catname'])) ? $cat['catname'] : ""; ?></span>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php endforeach; endif; ?>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- 中间新闻列表 -->
            <div class="news-main">


                <!-- 筛选按钮 -->
                <div class="news-filter">
                    <div class="filter-buttons">
                        <?php if($catid > 0): ?>
                        <?php if($current_cat['pinyin']): ?>
                        <a href="/news/<?php echo (isset($current_cat['pinyin'])) ? $current_cat['pinyin'] : ""; ?>/" class="filter-btn <?php if($filter_recommend == 0 && $filter_top == 0): ?>active<?php endif; ?>">全部</a>
                        <a href="/news/<?php echo (isset($current_cat['pinyin'])) ? $current_cat['pinyin'] : ""; ?>/?recommend=1" class="filter-btn <?php if($filter_recommend == 1): ?>active<?php endif; ?>">推荐</a>
                        <a href="/news/<?php echo (isset($current_cat['pinyin'])) ? $current_cat['pinyin'] : ""; ?>/?top=1" class="filter-btn <?php if($filter_top == 1): ?>active<?php endif; ?>">置顶</a>
                        <?php else: ?>
                        <a href="/news/?catid=<?php echo $catid ?? ""; ?>" class="filter-btn <?php if($filter_recommend == 0 && $filter_top == 0): ?>active<?php endif; ?>">全部</a>
                        <a href="/news/?catid=<?php echo $catid ?? ""; ?>&recommend=1" class="filter-btn <?php if($filter_recommend == 1): ?>active<?php endif; ?>">推荐</a>
                        <a href="/news/?catid=<?php echo $catid ?? ""; ?>&top=1" class="filter-btn <?php if($filter_top == 1): ?>active<?php endif; ?>">置顶</a>
                        <?php endif; ?>
                        <?php else: ?>
                        <a href="/news/" class="filter-btn <?php if($filter_recommend == 0 && $filter_top == 0): ?>active<?php endif; ?>">全部</a>
                        <a href="/news/?recommend=1" class="filter-btn <?php if($filter_recommend == 1): ?>active<?php endif; ?>">推荐</a>
                        <a href="/news/?top=1" class="filter-btn <?php if($filter_top == 1): ?>active<?php endif; ?>">置顶</a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="news-list">
                    <?php if($news_list): ?>
                    <?php if(null !== ($news_list ?? null) && is_array($news_list)): foreach($news_list as $news): ?>
                    <!-- 统一新闻列表项 -->
                    <div class="news-item <?php if(null !== ($news ?? null) && is_array($news) && array_key_exists('thumb', $news) && $news['thumb']): ?>has-thumb<?php else: ?>no-thumb<?php endif; ?>">
                        <?php if(null !== ($news ?? null) && is_array($news) && array_key_exists('thumb', $news) && $news['thumb']): ?>
                        <!-- 左侧缩略图 -->
                        <div class="news-thumb">
                            <a href="/news/<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>.html">
                                <img src="<?php echo (isset($news['thumb'])) ? $news['thumb'] : ""; ?>" alt="<?php echo (isset($news['title'])) ? $news['title'] : ""; ?>">
                            </a>
                        </div>
                        <?php endif; ?>

                        <!-- 新闻内容 -->
                        <div class="news-content">
                            <h3>
                                <a href="/news/<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>.html"><?php echo (isset($news['title'])) ? $news['title'] : ""; ?></a>
                                <?php if($news['is_top']): ?><span class="top-tag">置顶</span><?php endif; ?>
                                <?php if($news['is_recommend']): ?><span class="rec-tag">推荐</span><?php endif; ?>
                            </h3>
                            <div class="meta">
                                <span>作者：<?php if($news['author']): ?><?php echo (isset($news['author'])) ? $news['author'] : ""; ?><?php else: ?>admin<?php endif; ?></span>
                                <span>浏览：<?php echo (isset($news['click'])) ? $news['click'] : ""; ?> 人浏览</span>
                                <span><?php echo date('Y-m-d', $news['addtime']); ?></span>
                            </div>
                            <div class="desc">
                                <?php
                                if (!empty($news['description'])) {
                                    echo cut_str($news['description'], 150);
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; endif; ?>
                    <?php else: ?>
                    <div class="empty-list" style="padding: 50px 0; text-align: center; color: #999;">
                        <p>暂无相关新闻</p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- 分页 -->
                <?php if($pagebar): ?>
                <?php echo $pagebar ?? ""; ?>
                <?php endif; ?>
            </div>


        </div>
    </div>
    
    <div class="yui-footer">
    <div class="yui-1200">
        <div class="footer-content bg-white">
            <p class="footer-nav">
                <a href="https://www.botou.net/" title="泊头生活网">网站首页</a>
                <a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank">广告服务</a>
                <a href="https://www.botou.net/aboutus/shenmin.html" target="_blank">法律声明</a>
                <a href="https://www.botou.net/aboutus/about.html" target="_blank">网站介绍</a>
                <a href="https://www.botou.net/aboutus/contactus.html" target="_blank">联系我们</a>
                <a href="https://www.botou.net/aboutus/job.html" target="_blank">招聘信息</a>
            </p>
            <p class="footer-disclaimer">2本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
            <p class="footer-disclaimer">客服电话： &nbsp; 客服邮箱：<font><EMAIL></font> <a href="http://cyberpolice.mps.gov.cn/wfjb/" target="_blank" rel="nofollow">网络违法犯罪举报网站</a></p>
            <p class="footer-copyright">Copyright © 2006-2023 <a href="https://www.botou.net" title="泊头生活网" class="db_link">泊头生活网</a> 版权所有</p>
            <p class="footer-copyright"><a href="https://beian.miit.gov.cn/" target="_blank" id="footericp" rel="nofollow">冀ICP备2023009391号-1</a> <a rel="nofollow" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=13098102000307">冀公网安备 13098102000307号</a></p>
        </div>
    </div>
</div>

    <script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
    <script type="text/javascript" src="/template/pc/js/common.js"></script>

    <script>
    // 设置当前菜单项激活状态
    $(document).ready(function() {
        var currentUrl = window.location.href;
        var currentPath = window.location.pathname + window.location.search;

        $('.category-item a').each(function() {
            var linkUrl = $(this).attr('href');
            var linkPath = linkUrl.replace(window.location.origin, '');

            if (currentPath === linkPath ||
                (currentPath.indexOf('catid=') !== -1 && linkUrl.indexOf(currentPath.split('catid=')[1]) !== -1)) {
                $(this).parent().addClass('active');
            }
        });

        // 如果没有匹配的，默认激活第一个（热门）
        if (!$('.category-item.active').length) {
            $('.category-item').first().addClass('active');
        }
    });
    </script>
</body>
</html> 